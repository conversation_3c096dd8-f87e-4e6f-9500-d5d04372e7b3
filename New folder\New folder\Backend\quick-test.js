const axios = require('axios');

const BASE_URL = 'http://localhost:5002';

const test = async (name, url, method = 'GET', data = null) => {
  try {
    const config = { method, url: `${BASE_URL}${url}` };
    if (data) config.data = data;
    
    const response = await axios(config);
    console.log(`✅ ${name}: ${response.status}`);
    return response.data;
  } catch (error) {
    console.log(`❌ ${name}: ${error.response?.status || 'ERROR'} - ${error.message}`);
    return null;
  }
};

const runTests = async () => {
  console.log('🚀 Testing APIs...\n');
  
  // Basic tests
  await test('Health Check', '/health');
  await test('Root Endpoint', '/');
  await test('Jobs API', '/api/jobs');
  await test('Categories API', '/api/categories');
  await test('States API', '/api/states');
  await test('Blogs API', '/api/blogs');
  
  console.log('\n✅ API testing complete!');
};

// Check if server is running first
axios.get(`${BASE_URL}/health`)
  .then(() => {
    console.log('✅ Server is running\n');
    runTests();
  })
  .catch(() => {
    console.log('❌ Server not running. Start with: node server.js');
  });