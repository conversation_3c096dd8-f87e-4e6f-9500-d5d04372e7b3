// Load environment variables FIRST
const dotenv = require('dotenv');
dotenv.config();

const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const compression = require('compression');
const rateLimit = require('express-rate-limit');
const connectDB = require('./config/database');
const { connectRedis } = require('./config/redis');
const { cacheConfigs, warmUpCache } = require('./middleware/cache');
const { rateLimiters, whitelistMiddleware, blockSuspiciousIPs } = require('./middleware/rateLimiter');
const { performanceMonitor, requestCounter, errorTracker, getHealthMetrics, getAnalytics } = require('./middleware/monitoring');
const errorHandler = require('./middleware/errorHandler');
// const { verifyEmailConfig } = require('./utils/emailService');

// Connect to database and Redis
connectDB();
connectRedis();

// Initialize Express app
const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// Compression middleware
app.use(compression({
  level: parseInt(process.env.COMPRESSION_LEVEL) || 6,
  threshold: 1024,
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  }
}));

// IP whitelist and suspicious IP blocking
app.use(whitelistMiddleware);
app.use(blockSuspiciousIPs);

// Rate limiting with Redis support
app.use('/api/', rateLimiters.general);

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = [
      process.env.FRONTEND_URL,
      'http://localhost:3000',
      'http://localhost:3001'
    ];
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Performance monitoring middleware
app.use(performanceMonitor);
app.use(requestCounter);

// Request logging middleware (development only)
if (process.env.NODE_ENV === 'development') {
  app.use((req, res, next) => {
    console.log(`${req.method} ${req.originalUrl} - ${new Date().toISOString()}`);
    next();
  });
}

// Health check route with metrics
app.get('/health', async (req, res) => {
  try {
    const metrics = await getHealthMetrics();
    res.status(200).json({
      success: true,
      message: 'Server is running',
      ...metrics
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
});

// Analytics route (admin only)
app.get('/api/analytics', rateLimiters.auth, async (req, res) => {
  try {
    const days = parseInt(req.query.days) || 7;
    const analytics = await getAnalytics(days);
    res.status(200).json({
      success: true,
      data: analytics
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: 'Failed to get analytics',
      error: error.message
    });
  }
});

// API routes with specific rate limiting and caching
app.use('/api/admin', rateLimiters.auth, require('./routes/auth'));
app.use('/api/jobs', cacheConfigs.jobs, require('./routes/jobs'));
app.use('/api/categories', cacheConfigs.categories, require('./routes/categories'));
app.use('/api/states', cacheConfigs.states, require('./routes/states'));
app.use('/api/blogs', cacheConfigs.blogs, require('./routes/blogs'));
app.use('/api/subscribe', rateLimiters.subscription, require('./routes/subscribers'));
app.use('/api/subscribers', rateLimiters.subscription, require('./routes/subscribers'));

// Root route
app.get('/', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Welcome to Sarkari Jobs & Alerts API',
    version: '1.0.0',
    documentation: '/api/docs',
    endpoints: {
      auth: '/api/admin',
      jobs: '/api/jobs',
      categories: '/api/categories',
      states: '/api/states',
      blogs: '/api/blogs',
      subscription: '/api/subscribe',
      subscribers: '/api/subscribers'
    }
  });
});

// Handle 404 routes
app.all('*', (req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`
  });
});

// Error tracking middleware
app.use(errorTracker);

// Error handling middleware (must be last)
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, async () => {
  console.log(`🚀 Server running in ${process.env.NODE_ENV} mode on port ${PORT}`);
  console.log(`👥 Process ID: ${process.pid}`);
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`);

  // Verify email configuration on startup (disabled for now)
  // const emailConfigValid = await verifyEmailConfig();
  // if (emailConfigValid) {
  //   console.log('✅ Email service configured successfully');
  // } else {
  //   console.log('⚠️  Email service configuration failed - check your email settings');
  // }
  console.log('📧 Email verification disabled for development');

  // Warm up cache after server starts
  if (process.env.NODE_ENV === 'production') {
    setTimeout(() => {
      warmUpCache().catch(console.error);
    }, 5000); // Wait 5 seconds before warming up cache
  }
});

// Store server reference globally for graceful shutdown
global.server = server;

// Graceful shutdown function
const gracefulShutdown = (signal) => {
  console.log(`📴 Received ${signal}. Graceful shutdown...`);

  server.close(() => {
    console.log('✅ HTTP server closed');

    // Close database connection
    if (global.mongoose) {
      global.mongoose.connection.close(() => {
        console.log('✅ Database connection closed');
        process.exit(0);
      });
    } else {
      process.exit(0);
    }
  });

  // Force close after 10 seconds
  setTimeout(() => {
    console.log('💀 Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

// Handle shutdown signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  console.error('💥 Uncaught Exception:', err);
  gracefulShutdown('uncaughtException');
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (err) => {
  console.error('💥 Unhandled Rejection:', err);
  gracefulShutdown('unhandledRejection');
});

module.exports = app;
