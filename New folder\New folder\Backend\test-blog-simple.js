const express = require('express');
const mongoose = require('mongoose');
require('dotenv').config();

const app = express();

// Connect to MongoDB
mongoose.connect(process.env.MONGODB_URI)
  .then(() => console.log('MongoDB connected'))
  .catch(err => console.error('MongoDB error:', err));

// Simple blog route
app.get('/api/blogs', async (req, res) => {
  try {
    res.status(200).json({
      success: true,
      count: 0,
      data: [],
      message: 'No blogs found - this is expected for empty database'
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message
    });
  }
});

app.listen(5003, () => {
  console.log('Test server running on port 5003');
});