const Blog = require('../models/Blog');
const { validationResult } = require('express-validator');

// @desc    Get all blogs (public)
// @route   GET /api/blogs
// @access  Public
const getBlogs = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      category,
      status,
      featured,
      search,
      sort = '-createdAt',
      author
    } = req.query;

    // Build filter
    let filter = {};
    
    // For public access, only show published blogs
    if (!req.admin) {
      filter.status = 'published';
    } else if (status) {
      filter.status = status;
    }

    if (category) filter.category = category;
    if (featured !== undefined) filter.featured = featured === 'true';
    if (author) filter.author = author;

    // Search functionality
    if (search) {
      filter.$or = [
        { title: { $regex: search, $options: 'i' } },
        { content: { $regex: search, $options: 'i' } },
        { excerpt: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Simple pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Execute query with error handling
    const blogs = await Blog.find(filter)
      .populate('author', 'name email')
      .select('-viewsHistory -content')
      .sort(sort)
      .skip(skip)
      .limit(limitNum)
      .lean(); // Use lean for better performance

    const total = await Blog.countDocuments(filter);

    res.status(200).json({
      success: true,
      count: blogs.length,
      pagination: {
        page: pageNum,
        pages: Math.ceil(total / limitNum) || 1,
        total
      },
      data: blogs
    });
  } catch (error) {
    console.error('Get blogs error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching blogs',
      error: error.message
    });
  }
};

// @desc    Get single blog
// @route   GET /api/blogs/:id
// @access  Public
const getBlog = async (req, res) => {
  try {
    const blog = await Blog.findById(req.params.id)
      .populate('author', 'name email role');

    if (!blog) {
      return res.status(404).json({
        success: false,
        message: 'Blog not found'
      });
    }

    // Check if user can access this blog
    if (blog.status !== 'published' && !req.admin) {
      return res.status(403).json({
        success: false,
        message: 'Blog not available'
      });
    }

    // Increment views for published blogs (only for non-admin users)
    if (blog.status === 'published' && !req.admin) {
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');
      await blog.incrementViews(ipAddress, userAgent);
    }

    res.status(200).json({
      success: true,
      data: blog
    });
  } catch (error) {
    console.error('Get blog error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching blog'
    });
  }
};

// @desc    Get blog by slug
// @route   GET /api/blogs/slug/:slug
// @access  Public
const getBlogBySlug = async (req, res) => {
  try {
    const blog = await Blog.findOne({ slug: req.params.slug })
      .populate('author', 'name email role');

    if (!blog) {
      return res.status(404).json({
        success: false,
        message: 'Blog not found'
      });
    }

    // Check if user can access this blog
    if (blog.status !== 'published' && !req.admin) {
      return res.status(403).json({
        success: false,
        message: 'Blog not available'
      });
    }

    // Increment views for published blogs (only for non-admin users)
    if (blog.status === 'published' && !req.admin) {
      const ipAddress = req.ip || req.connection.remoteAddress;
      const userAgent = req.get('User-Agent');
      await blog.incrementViews(ipAddress, userAgent);
    }

    res.status(200).json({
      success: true,
      data: blog
    });
  } catch (error) {
    console.error('Get blog by slug error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching blog'
    });
  }
};

// @desc    Create new blog
// @route   POST /api/blogs
// @access  Private (Admin only)
const createBlog = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const blogData = {
      ...req.body,
      author: req.admin._id
    };

    // Generate excerpt if not provided
    if (!blogData.excerpt && blogData.content) {
      blogData.excerpt = blogData.content
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .substring(0, 200) + '...';
    }

    const blog = await Blog.create(blogData);
    
    // Populate author info
    await blog.populate('author', 'name email');

    res.status(201).json({
      success: true,
      message: 'Blog created successfully',
      data: blog
    });
  } catch (error) {
    console.error('Create blog error:', error);
    
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Blog with this slug already exists'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Error creating blog'
    });
  }
};

// @desc    Update blog
// @route   PUT /api/blogs/:id
// @access  Private (Admin only)
const updateBlog = async (req, res) => {
  try {
    const blog = await Blog.findById(req.params.id);

    if (!blog) {
      return res.status(404).json({
        success: false,
        message: 'Blog not found'
      });
    }

    // Update blog
    Object.assign(blog, req.body);
    
    // Generate excerpt if not provided
    if (!req.body.excerpt && req.body.content) {
      blog.excerpt = req.body.content
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .substring(0, 200) + '...';
    }

    await blog.save();
    await blog.populate('author', 'name email');

    res.status(200).json({
      success: true,
      message: 'Blog updated successfully',
      data: blog
    });
  } catch (error) {
    console.error('Update blog error:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating blog'
    });
  }
};

// @desc    Delete blog
// @route   DELETE /api/blogs/:id
// @access  Private (Admin only)
const deleteBlog = async (req, res) => {
  try {
    const blog = await Blog.findById(req.params.id);

    if (!blog) {
      return res.status(404).json({
        success: false,
        message: 'Blog not found'
      });
    }

    await blog.deleteOne();

    res.status(200).json({
      success: true,
      message: 'Blog deleted successfully'
    });
  } catch (error) {
    console.error('Delete blog error:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting blog'
    });
  }
};

// @desc    Get popular blogs
// @route   GET /api/blogs/popular
// @access  Public
const getPopularBlogs = async (req, res) => {
  try {
    const { limit = 10, days = 7 } = req.query;
    
    const popularBlogs = await Blog.getPopularBlogs(parseInt(limit), parseInt(days));

    res.status(200).json({
      success: true,
      count: popularBlogs.length,
      data: popularBlogs
    });
  } catch (error) {
    console.error('Get popular blogs error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching popular blogs'
    });
  }
};

// @desc    Get blog statistics
// @route   GET /api/blogs/stats
// @access  Private (Admin only)
const getBlogStats = async (req, res) => {
  try {
    const stats = await Blog.getBlogStats();
    
    // Get category-wise stats
    const categoryStats = await Blog.aggregate([
      { $match: { status: 'published' } },
      { $group: {
        _id: '$category',
        count: { $sum: 1 },
        totalViews: { $sum: '$views' }
      }},
      { $sort: { count: -1 } }
    ]);

    // Get recent activity
    const recentBlogs = await Blog.find()
      .populate('author', 'name')
      .select('title status createdAt author')
      .sort('-createdAt')
      .limit(10);

    res.status(200).json({
      success: true,
      data: {
        overview: stats[0] || {
          totalBlogs: 0,
          publishedBlogs: 0,
          draftBlogs: 0,
          totalViews: 0,
          averageViews: 0
        },
        categoryStats,
        recentActivity: recentBlogs
      }
    });
  } catch (error) {
    console.error('Get blog stats error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching blog statistics'
    });
  }
};

// @desc    Get featured blogs
// @route   GET /api/blogs/featured
// @access  Public
const getFeaturedBlogs = async (req, res) => {
  try {
    const { limit = 5 } = req.query;

    const featuredBlogs = await Blog.find({
      status: 'published',
      featured: true
    })
      .populate('author', 'name')
      .select('-content -viewsHistory')
      .sort('-publishedAt')
      .limit(parseInt(limit));

    res.status(200).json({
      success: true,
      count: featuredBlogs.length,
      data: featuredBlogs
    });
  } catch (error) {
    console.error('Get featured blogs error:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching featured blogs'
    });
  }
};

module.exports = {
  getBlogs,
  getBlog,
  getBlogBySlug,
  createBlog,
  updateBlog,
  deleteBlog,
  getPopularBlogs,
  getBlogStats,
  getFeaturedBlogs
};
