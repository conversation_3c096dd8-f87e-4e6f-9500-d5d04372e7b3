{"name": "sarkari-jobs-backend", "version": "1.0.0", "description": "Backend API for Sarkari Jobs & Alerts Website", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node seeders/seedData.js", "cluster": "node cluster.js", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop ecosystem.config.js", "pm2:restart": "pm2 restart ecosystem.config.js", "pm2:delete": "pm2 delete ecosystem.config.js", "pm2:logs": "pm2 logs", "pm2:monitor": "pm2 monit", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["sarkari", "jobs", "alerts", "government", "api"], "author": "Your Name", "license": "ISC", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^2.4.3", "cluster": "^0.7.7", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "pm2": "^5.4.3", "redis": "^4.7.1"}, "devDependencies": {"nodemon": "^3.0.1"}}