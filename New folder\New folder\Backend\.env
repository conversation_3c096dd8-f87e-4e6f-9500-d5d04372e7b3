# Database Configuration
MONGODB_URI=mongodb+srv://prakisingh14:<EMAIL>/sarkari_jobs_db
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/sarkari_jobs_db

# Server Configuration
PORT=5001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
JWT_EXPIRE=7d

# Email Configuration (for alerts)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=sjxflyzbrefafp
EMAIL_FROM=<EMAIL>

# CORS Configuration
FRONTEND_URL=http://localhost:3000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=1000

# Redis Configuration (Redis Cloud)
REDIS_HOST=redis-10611.c82.us-east-1-2.ec2.redns.redis-cloud.com
REDIS_PORT=10611
REDIS_USERNAME=default
REDIS_PASSWORD=a1tlzSfoh7iQ9kecIqF4ndjCJQQ7O7IV
REDIS_DB=0
REDIS_URL=redis://default:<EMAIL>:10611
USE_REDIS=false

NODE_ENV=development
CLUSTER_WORKERS=0
COMPRESSION_LEVEL=6
CACHE_TTL=300

# Traffic Management
MAX_DAILY_USERS=10000
HIGH_TRAFFIC_THRESHOLD=500
ENABLE_MONITORING=true
