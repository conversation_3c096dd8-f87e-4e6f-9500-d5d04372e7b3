const redis = require('redis');

// Redis configuration for caching and session management
const redisConfig = process.env.REDIS_URL ? {
  url: process.env.REDIS_URL
} : {
  socket: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
  },
  username: process.env.REDIS_USERNAME || 'default',
  password: process.env.REDIS_PASSWORD || undefined,
  database: parseInt(process.env.REDIS_DB) || 0,
};

// Create Redis client
let redisClient = null;

const connectRedis = async () => {
  try {
    if (process.env.NODE_ENV === 'production' || process.env.USE_REDIS === 'true') {
      redisClient = redis.createClient(redisConfig);
      
      redisClient.on('error', (err) => {
        console.error('Redis Client Error:', err);
      });

      redisClient.on('connect', () => {
        console.log('✅ Redis connected successfully');
      });

      redisClient.on('ready', () => {
        console.log('✅ Redis ready for operations');
      });

      redisClient.on('end', () => {
        console.log('❌ Redis connection ended');
      });

      await redisClient.connect();
      console.log('🔗 Redis client connected to cloud instance');
    } else {
      console.log('📝 Redis disabled - using in-memory cache for development');
    }
  } catch (error) {
    console.error('❌ Redis connection failed:', error.message);
    console.log('📝 Falling back to in-memory cache');
  }
};

// In-memory cache fallback for development
const memoryCache = new Map();
const cacheExpiry = new Map();

// Cache operations with Redis fallback to memory
const cache = {
  // Set cache with TTL (Time To Live)
  set: async (key, value, ttlSeconds = 3600) => {
    try {
      if (redisClient && redisClient.isReady) {
        await redisClient.setEx(key, ttlSeconds, JSON.stringify(value));
      } else {
        // Fallback to memory cache
        memoryCache.set(key, value);
        cacheExpiry.set(key, Date.now() + (ttlSeconds * 1000));
      }
      return true;
    } catch (error) {
      console.error('Cache set error:', error);
      return false;
    }
  },

  // Get cache
  get: async (key) => {
    try {
      if (redisClient && redisClient.isReady) {
        const value = await redisClient.get(key);
        return value ? JSON.parse(value) : null;
      } else {
        // Fallback to memory cache
        if (memoryCache.has(key)) {
          const expiry = cacheExpiry.get(key);
          if (Date.now() < expiry) {
            return memoryCache.get(key);
          } else {
            // Expired
            memoryCache.delete(key);
            cacheExpiry.delete(key);
            return null;
          }
        }
        return null;
      }
    } catch (error) {
      console.error('Cache get error:', error);
      return null;
    }
  },

  // Delete cache
  del: async (key) => {
    try {
      if (redisClient && redisClient.isReady) {
        await redisClient.del(key);
      } else {
        memoryCache.delete(key);
        cacheExpiry.delete(key);
      }
      return true;
    } catch (error) {
      console.error('Cache delete error:', error);
      return false;
    }
  },

  // Clear all cache
  clear: async () => {
    try {
      if (redisClient && redisClient.isReady) {
        await redisClient.flushDb();
      } else {
        memoryCache.clear();
        cacheExpiry.clear();
      }
      return true;
    } catch (error) {
      console.error('Cache clear error:', error);
      return false;
    }
  },

  // Check if key exists
  exists: async (key) => {
    try {
      if (redisClient && redisClient.isReady) {
        return await redisClient.exists(key);
      } else {
        if (memoryCache.has(key)) {
          const expiry = cacheExpiry.get(key);
          if (Date.now() < expiry) {
            return true;
          } else {
            memoryCache.delete(key);
            cacheExpiry.delete(key);
            return false;
          }
        }
        return false;
      }
    } catch (error) {
      console.error('Cache exists error:', error);
      return false;
    }
  },

  // Get cache with pattern
  keys: async (pattern) => {
    try {
      if (redisClient && redisClient.isReady) {
        return await redisClient.keys(pattern);
      } else {
        // Simple pattern matching for memory cache
        const keys = Array.from(memoryCache.keys());
        if (pattern === '*') return keys;
        
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return keys.filter(key => regex.test(key));
      }
    } catch (error) {
      console.error('Cache keys error:', error);
      return [];
    }
  },

  // Increment counter (for rate limiting)
  incr: async (key, ttlSeconds = 3600) => {
    try {
      if (redisClient && redisClient.isReady) {
        const value = await redisClient.incr(key);
        if (value === 1) {
          await redisClient.expire(key, ttlSeconds);
        }
        return value;
      } else {
        // Fallback to memory cache
        const current = memoryCache.get(key) || 0;
        const newValue = current + 1;
        memoryCache.set(key, newValue);
        
        if (current === 0) {
          cacheExpiry.set(key, Date.now() + (ttlSeconds * 1000));
        }
        return newValue;
      }
    } catch (error) {
      console.error('Cache incr error:', error);
      return 1;
    }
  }
};

// Cleanup memory cache periodically
setInterval(() => {
  const now = Date.now();
  for (const [key, expiry] of cacheExpiry.entries()) {
    if (now >= expiry) {
      memoryCache.delete(key);
      cacheExpiry.delete(key);
    }
  }
}, 60000); // Clean every minute

// Graceful shutdown
const closeRedis = async () => {
  if (redisClient && redisClient.isReady) {
    await redisClient.quit();
    console.log('✅ Redis connection closed');
  }
};

module.exports = {
  connectRedis,
  closeRedis,
  cache,
  redisClient: () => redisClient
};
