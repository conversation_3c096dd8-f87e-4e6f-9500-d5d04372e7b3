const axios = require('axios');

const BASE_URL = 'http://localhost:5002';
let authToken = '';
let testIds = {};

const apiCall = async (method, endpoint, data = null, headers = {}) => {
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: { 'Content-Type': 'application/json', ...headers }
    };
    if (data) config.data = data;
    
    const response = await axios(config);
    return { success: true, status: response.status, data: response.data };
  } catch (error) {
    return {
      success: false,
      status: error.response?.status || 500,
      data: error.response?.data || { message: error.message }
    };
  }
};

const runComprehensiveTests = async () => {
  console.log('🚀 Running Comprehensive API Tests...\n');
  
  const tests = [];
  
  // 1. Server Health and Info
  tests.push({
    name: 'Server Health Check',
    test: async () => {
      const result = await apiCall('GET', '/health');
      return result.success && result.status === 200;
    }
  });

  tests.push({
    name: 'Root Endpoint with Stats',
    test: async () => {
      const result = await apiCall('GET', '/');
      return result.success && result.data.stats;
    }
  });

  // 2. Authentication Flow
  tests.push({
    name: 'Admin Registration',
    test: async () => {
      const result = await apiCall('POST', '/api/admin/register', {
        name: 'Test Admin',
        email: '<EMAIL>',
        password: 'password123',
        role: 'admin'
      });
      if (result.success && result.data.data.token) {
        authToken = result.data.data.token;
        return true;
      }
      return false;
    }
  });

  tests.push({
    name: 'Duplicate Admin Registration (Should Fail)',
    test: async () => {
      const result = await apiCall('POST', '/api/admin/register', {
        name: 'Test Admin 2',
        email: '<EMAIL>',
        password: 'password123'
      });
      return !result.success && result.status === 400;
    }
  });

  tests.push({
    name: 'Admin Login',
    test: async () => {
      const result = await apiCall('POST', '/api/admin/login', {
        email: '<EMAIL>',
        password: 'password123'
      });
      return result.success && result.data.data.token;
    }
  });

  tests.push({
    name: 'Invalid Login Credentials',
    test: async () => {
      const result = await apiCall('POST', '/api/admin/login', {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });
      return !result.success && result.status === 401;
    }
  });

  tests.push({
    name: 'Get Admin Profile',
    test: async () => {
      const result = await apiCall('GET', '/api/admin/profile', null, {
        'Authorization': `Bearer ${authToken}`
      });
      return result.success && result.data.data.email === '<EMAIL>';
    }
  });

  // 3. Category Management
  tests.push({
    name: 'Create Category',
    test: async () => {
      const result = await apiCall('POST', '/api/categories', {
        name: 'Banking Jobs',
        description: 'All banking related jobs',
        sortOrder: 1
      }, { 'Authorization': `Bearer ${authToken}` });
      
      if (result.success && result.data.data._id) {
        testIds.categoryId = result.data.data._id;
        return true;
      }
      return false;
    }
  });

  tests.push({
    name: 'Get Categories',
    test: async () => {
      const result = await apiCall('GET', '/api/categories');
      return result.success && result.data.count > 0;
    }
  });

  tests.push({
    name: 'Create Category Without Auth (Should Fail)',
    test: async () => {
      const result = await apiCall('POST', '/api/categories', {
        name: 'Unauthorized Category',
        description: 'This should fail'
      });
      return !result.success && result.status === 401;
    }
  });

  // 4. State Management
  tests.push({
    name: 'Create State',
    test: async () => {
      const result = await apiCall('POST', '/api/states', {
        name: 'Maharashtra',
        code: 'MH',
        region: 'West',
        sortOrder: 1
      }, { 'Authorization': `Bearer ${authToken}` });
      
      if (result.success && result.data.data._id) {
        testIds.stateId = result.data.data._id;
        return true;
      }
      return false;
    }
  });

  tests.push({
    name: 'Get States',
    test: async () => {
      const result = await apiCall('GET', '/api/states');
      return result.success && result.data.count > 0;
    }
  });

  tests.push({
    name: 'Get States by Region',
    test: async () => {
      const result = await apiCall('GET', '/api/states?region=West');
      return result.success && result.data.data.length > 0;
    }
  });

  // 5. Job Management
  tests.push({
    name: 'Create Job',
    test: async () => {
      const result = await apiCall('POST', '/api/jobs', {
        title: 'SBI Clerk Recruitment 2024',
        department: 'State Bank of India',
        vacancies: 5000,
        qualification: 'Graduate',
        ageLimit: { min: 20, max: 28 },
        fees: { general: 750, obc: 750, sc_st: 0 },
        state: testIds.stateId,
        category: testIds.categoryId,
        importantDates: {
          applicationStart: '2024-01-01',
          applicationEnd: '2024-01-31',
          examDate: '2024-03-15'
        },
        applyLink: 'https://sbi.co.in/careers',
        notificationLink: 'https://sbi.co.in/notifications',
        description: 'SBI Clerk recruitment for 2024',
        status: 'active',
        featured: true
      }, { 'Authorization': `Bearer ${authToken}` });
      
      if (result.success && result.data.data._id) {
        testIds.jobId = result.data.data._id;
        return true;
      }
      return false;
    }
  });

  tests.push({
    name: 'Get All Jobs',
    test: async () => {
      const result = await apiCall('GET', '/api/jobs');
      return result.success && result.data.count > 0;
    }
  });

  tests.push({
    name: 'Get Jobs with Pagination',
    test: async () => {
      const result = await apiCall('GET', '/api/jobs?page=1&limit=5');
      return result.success && result.data.pagination;
    }
  });

  tests.push({
    name: 'Get Jobs with Category Filter',
    test: async () => {
      const result = await apiCall('GET', `/api/jobs?category=${testIds.categoryId}`);
      return result.success && result.data.data.length > 0;
    }
  });

  tests.push({
    name: 'Get Jobs with State Filter',
    test: async () => {
      const result = await apiCall('GET', `/api/jobs?state=${testIds.stateId}`);
      return result.success && result.data.data.length > 0;
    }
  });

  tests.push({
    name: 'Search Jobs',
    test: async () => {
      const result = await apiCall('GET', '/api/jobs?search=SBI');
      return result.success && result.data.data.length > 0;
    }
  });

  tests.push({
    name: 'Get Featured Jobs',
    test: async () => {
      const result = await apiCall('GET', '/api/jobs?featured=true');
      return result.success && result.data.data.length > 0;
    }
  });

  // 6. Subscription Management
  tests.push({
    name: 'Subscribe for Alerts',
    test: async () => {
      const result = await apiCall('POST', '/api/subscribe', {
        email: '<EMAIL>',
        mobile: '9876543210',
        preferences: {
          categories: [testIds.categoryId],
          states: [testIds.stateId],
          emailAlerts: true,
          smsAlerts: false
        }
      });
      return result.success && result.data.data.verificationRequired;
    }
  });

  tests.push({
    name: 'Subscribe with Duplicate Email',
    test: async () => {
      const result = await apiCall('POST', '/api/subscribe', {
        email: '<EMAIL>',
        preferences: {
          categories: [testIds.categoryId],
          emailAlerts: true
        }
      });
      // Should still succeed but might have different behavior
      return result.success;
    }
  });

  // 7. Error Handling Tests
  tests.push({
    name: 'Invalid JSON Request',
    test: async () => {
      try {
        const response = await axios.post(`${BASE_URL}/api/admin/login`, 'invalid json', {
          headers: { 'Content-Type': 'application/json' }
        });
        return false;
      } catch (error) {
        return error.response?.status === 400;
      }
    }
  });

  tests.push({
    name: 'Missing Required Fields',
    test: async () => {
      const result = await apiCall('POST', '/api/admin/register', {
        name: 'Test User'
        // Missing email and password
      });
      return !result.success;
    }
  });

  tests.push({
    name: 'Invalid Route (404)',
    test: async () => {
      const result = await apiCall('GET', '/api/nonexistent');
      return result.status === 404;
    }
  });

  tests.push({
    name: 'Unauthorized Access',
    test: async () => {
      const result = await apiCall('POST', '/api/jobs', {
        title: 'Unauthorized Job'
      });
      return result.status === 401;
    }
  });

  tests.push({
    name: 'Invalid Token',
    test: async () => {
      const result = await apiCall('GET', '/api/admin/profile', null, {
        'Authorization': 'Bearer invalid_token'
      });
      return result.status === 401;
    }
  });

  // Run all tests
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    try {
      console.log(`🔍 ${test.name}...`);
      const result = await test.test();
      if (result) {
        console.log(`✅ ${test.name} - PASSED`);
        passed++;
      } else {
        console.log(`❌ ${test.name} - FAILED`);
        failed++;
      }
    } catch (error) {
      console.log(`❌ ${test.name} - ERROR: ${error.message}`);
      failed++;
    }
    
    // Small delay between tests
    await new Promise(resolve => setTimeout(resolve, 100));
  }
  
  console.log('\n📊 Comprehensive Test Results:');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);
  console.log(`🔢 Total Tests: ${passed + failed}`);
  
  if (failed === 0) {
    console.log('\n🎉 All comprehensive tests passed!');
  } else {
    console.log('\n⚠️  Some tests failed. Review the output above.');
  }
  
  console.log('\n📋 Test Coverage:');
  console.log('- ✅ Authentication & Authorization');
  console.log('- ✅ CRUD Operations (Categories, States, Jobs)');
  console.log('- ✅ Filtering & Pagination');
  console.log('- ✅ Search Functionality');
  console.log('- ✅ Subscription Management');
  console.log('- ✅ Error Handling');
  console.log('- ✅ Security (Unauthorized access)');
  console.log('- ✅ Data Validation');
};

// Check server and run tests
const checkAndRun = async () => {
  const health = await apiCall('GET', '/health');
  if (health.success) {
    console.log('✅ Server is running, starting comprehensive tests...\n');
    await runComprehensiveTests();
  } else {
    console.log('❌ Server is not running. Please start with: node test-server.js');
  }
};

checkAndRun().catch(console.error);
